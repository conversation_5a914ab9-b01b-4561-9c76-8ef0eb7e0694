@import '../../../client/styles/mixin';

.g-statistic {
  @include row-width-limit;
  margin: 0 auto .24rem;
  margin-top: 24px;
  min-width: 11.2rem;
  
  
  .content {
    -webkit-box-flex: 1;
    padding: 24px;
    width:100%;
    background: #fff;
    min-height: 5rem;
    // overflow-x: scroll;
  }
  .m-row {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 0;
  }

  .m-row-table {
    padding-top: 16px 

  }

  .statis-table {
    margin-left: 16px;
  }

  .m-help {
    margin-left: 5px;
    border-radius: 12px;
    color: #2395f1;
  }

  .gutter-row {
    padding-left: 24px;
    border-left: 1px solid #f0f0f0;
  }

  .gutter-row:first-child {
    border-left: 0
  }

  .gutter-box {
    margin-top: 8px;
    //margin-bottom: 16px;
    //margin: 8px 0 16px;
  }

  .statis-chart-content {
    margin-top: 8px;
  }

  .statis-title{
    padding: 8px 8px 24px;
  }

  .statis-chart{
    margin:0 auto;
    text-align: center;
  }

  .statis-footer{
    margin:16px 0;
    text-align: center;
    width: 1050px;
  }

  .title{
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 0.16rem;
    border-left: 3px solid #2395f1;
    padding-left: 8px;
  }

  .system-content{
    margin-bottom: 16px;
  }
}
