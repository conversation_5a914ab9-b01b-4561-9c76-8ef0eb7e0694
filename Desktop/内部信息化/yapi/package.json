{"name": "yapi-vendor", "version": "1.12.0", "description": "YAPI", "main": "server/app.js", "scripts": {"dev-copy-icon": "cp -r static/iconfont ./", "dev-server": " nodemon server/app.js dev -L", "install-server": " node server/install.js", "dev-client": "npm run dev-copy-icon && ykit s -p 4000", "dev": "npm run dev-server & npm run dev-client", "start": " node server/app.js", "test": "ava", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "build-client": "NODE_ENV=production ykit pack -m", "npm-publish": "node ./npm-publish.js", "docs": "ydoc build"}, "scripts-info": {"start": "运行生产环境服务器", "install-server": "初始化数据库数据，用于安装", "dev": "运行开发服务器", "dev-server": "运行后端开发服务器", "dev-client": "运行前端开发服务器", "test": "执行测试"}, "repository": {"type": "git", "url": "**************:YMFE/yapi.git"}, "author": "", "license": "Apache-2.0", "dependencies": {"ajv": "^5.5.1", "ajv-i18n": "^2.2.0", "axios": "0.18.1", "compare-versions": "^3.4.0", "copy-to-clipboard": "3.0.8", "cpu-load": "^1.0.0", "crypto-js": "^3.1.9-1", "deep-extend": "^0.5.0", "deref": "^0.7.3", "easy-json-schema": "0.0.2-beta", "fs-extra": "^3.0.1", "generate-schema": "^2.6.0", "immer": "^1.1.1", "js-base64": "^2.3.2", "json-schema-faker": "0.5.0-rc16", "json-schema-ref-parser": "4.0.0", "json5": "0.5.1", "jsondiffpatch": "0.3.11", "jsonwebtoken": "7.4.1", "jsrsasign": "^8.0.12", "koa": "2.0.0", "koa-body": "^2.5.0", "koa-bodyparser": "3.2.0", "koa-multer": "1.0.2", "koa-mysql-session": "0.0.2", "koa-router": "^7.0.1", "koa-send": "3.2.0", "koa-session-minimal": "3.0.3", "koa-static": "3.0.0", "koa-websocket": "4.0.0", "ldapjs": "^1.0.1", "markdown-it": "8.4.0", "markdown-it-anchor": "4.0.0", "markdown-it-table-of-contents": "0.3.2", "md5": "2.2.1", "mockjs": "1.0.1-beta3", "moment": "^2.19.3", "mongodb": "3.1.8", "mongoose": "5.7.5", "mongoose-auto-increment": "5.0.1", "moox": "^1.0.2", "node-schedule": "^1.3.2", "nodemailer": "4.0.1", "os": "0.1.1", "qs": "^6.7.0", "request": "2.81.0", "safeify": "^5.0.5", "sha.js": "2.4.9", "sha1": "1.1.1", "swagger-client": "^3.5.1", "tslib": "1.8.0", "underscore": "1.8.3", "url": "0.11.0", "vm2": "^3.8.4", "yapi-plugin-qsso": "^1.1.0"}, "devDependencies": {"antd": "3.2.2", "anujs": "^1.2.6", "assets-webpack-plugin": "^3.5.1", "ava": "^0.22.0", "babel": "^6.5.2", "babel-core": "^6.8.0", "babel-eslint": "^7.2.3", "babel-loader": "^6.2.4", "babel-plugin-import": "^1.3.1", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-object-rest-spread": "^6.8.0", "babel-plugin-transform-runtime": "^6.9.0", "babel-plugin-webpack-alias": "^2.1.2", "babel-preset-es2015": "^6.9.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-3": "^6.24.1", "babel-register": "^6.9.0", "babel-runtime": "^6.9.2", "brace": "^0.10.0", "buffer-shims": "^1.0.0", "compression-webpack-plugin": "^1.0.0", "conventional-changelog-cli": "^2.1.1", "copy-webpack-plugin": "^4.0.1", "core-decorators": "^0.17.0", "css-loader": "^0.28.4", "eslint": "^3.19.0", "eslint-loader": "^1.9.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-react": "^7.1.0", "extract-text-webpack-plugin": "2.0.0", "ghooks": "^2.0.0", "happypack": "^4.0.0-beta.5", "json-schema-editor-visual": "^1.0.23", "less": "^2.7.2", "less-loader": "^4.0.5", "markdown-it-include": "^1.0.0", "node-sass": "^4.9.0", "nodemon": "^1.11.0", "prop-types": "^15.5.10", "rc-queue-anim": "^1.2.0", "rc-scroll-anim": "^1.0.7", "rc-tween-one": "^1.5.5", "react": "^16.2.0", "react-dnd": "^2.5.1", "react-dnd-html5-backend": "^2.5.1", "react-dom": "^16.2.0", "react-redux": "^5.0.5", "react-router-dom": "^4.1.1", "react-scripts": "1.0.10", "reactabular-dnd": "^8.9.0", "reactabular-table": "^8.9.0", "recharts": "^1.0.0-beta.0", "redux": "^3.7.1", "redux-devtools": "^3.4.0", "redux-devtools-dock-monitor": "^1.1.2", "redux-devtools-log-monitor": "^1.3.0", "redux-promise": "^0.5.3", "rewire": "^2.5.2", "sass-loader": "^7.0.3", "string-replace-webpack-plugin": "^0.1.3", "style-loader": "^0.18.2", "table-resolver": "^3.2.0", "validate-commit-msg": "^2.12.2", "webpack": "^2.2.0", "webpack-dev-middleware": "^1.12.0", "webpack-node-externals": "^1.6.0", "ydoc-plugin-img-view": "^1.0.1", "ykit": "0.6.2", "ykit-config-antd": "0.1.3", "ykit-config-es6": "^1.1.0"}, "config": {"ghooks": {"commit-msg": "validate-commit-msg"}, "validate-commit-msg": {"types": ["feat", "fix", "docs", "test", "chore", "refactor", "opti"], "warnOnFail": false, "maxSubjectLength": 100, "subjectPattern": ".+", "subjectPatternErrorMsg": "请输入message信息!", "helpMessage": "Commit message 格式错误， http://www.ruanyifeng.com/blog/2016/01/commit_message_change_log.html"}}, "engines": {"node": ">= 7.6.0", "npm": ">= 4.1.2"}, "babel": {"presets": [["es2015", {"loose": true, "modules": "commonjs"}], "es2017", "stage-0", "react"], "plugins": ["transform-runtime", ["webpack-alias", {"config": "webpack.alias.js"}]]}, "ava": {"files": ["test/**/*.js"], "require": ["babel-register"], "babel": "inherit"}}