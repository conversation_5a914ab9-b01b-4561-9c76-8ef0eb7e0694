<!DOCTYPE html>
<html>
<head>
<meta  id="cross-request-sign"  charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="yapi接口管理,api管理,接口管理,api,接口,接口文档,api文档,接口管理系统" />
<meta name="description" content="YApi 是高效、易用、功能强大的 api 管理平台，旨在为开发、产品、测试人员提供更优雅的接口管理服务。可以帮助开发者轻松创建、发布、维护 API，YApi 还为用户提供了优秀的交互体验，开发人员只需利用平台提供的接口数据写入工具以及简单的点击操作就可以实现接口的管理。" />
<title>YApi-高效、易用、功能强大的可视化接口管理平台</title>
<link rel="icon" type="image/png" sizes="192x192" href="/image/favicon.png">
<script>
    document.write('<script src="/prd/assets.js?v=' + Math.random() + '"><\/script>');
</script>

<script>
    document.write('<link rel="stylesheet"  href="/prd/' + window.WEBPACK_ASSETS['index.js'].css + '" />');
</script>

</head>
<body>
<div id="yapi" style="height: 100%;"></div>


<script>
    document.write('<script src="/prd/' + window.WEBPACK_ASSETS['manifest'].js + '"><\/script>');
</script>
<script>
    document.write('<script src="/prd/' + window.WEBPACK_ASSETS['lib3'].js + '"><\/script>');
</script>
<script>
    document.write('<script src="/prd/' + window.WEBPACK_ASSETS['lib2'].js + '"><\/script>');
</script>
<script>
    document.write('<script src="/prd/' + window.WEBPACK_ASSETS['lib'].js + '"><\/script>');
</script>

<script>
    document.write('<script src="/prd/' + window.WEBPACK_ASSETS['index.js'].js + '"><\/script>');
</script>
</body>
</html>
