.modal-postman {
  .ant-modal-body {
    padding: 0;
  }
  .ant-modal-footer {
    background-color: #f5f5f5;
  }
  .modal-postman-form {
    // padding: 0 16px;
    max-height: 500px;
    min-height: 400px;
    overflow-y: scroll;
    .ant-radio-group{
      width:100%;
    }

    .mock-search {
      padding-right: 8px;
      margin-bottom: 16px;
    }
    .mock-checked{
      color:#fff;
      background-color:#2395f1;
      width:100%
    }
    .row {
      margin-bottom: 8px;
      width: 100%;
      padding: 8px 16px;
      cursor: pointer;
    }

    .checked{
      color:#fff;
      background-color:#2395f1;
    }
  }
  .modal-postman-expression, .modal-postman-preview {
    border-top: 1px solid #e9e9e9;
    padding: 8px 16px;
    line-height: 38px;
  }
  .modal-postman-preview {
    background-color: #f5f5f5;
    
  }
  .modal-postman-collapse{
     .ant-collapse-item > .ant-collapse-header{
      // padding-left: 20px;
      // margin-left: 8px;
      background-color: #f5f5f5;
    }

     .ant-collapse-item > .ant-collapse-header .arrow{
      left: 14px;
      font-size: 1.17em;
    }
    .ant-collapse-content > .ant-collapse-content-box{
      padding-top: 8px;
    }

    .ant-collapse-content {
      padding: 0 0 0 8px;
    }
  }

  .title {
    border-left: 3px solid #2395f1;
    padding-left: 8px;
  }

  .modal-postman-form-mock, .modal-postman-form-variable{
    max-height: 300px;
    overflow-y: scroll;
  }

  .mock-title, .methods-title{
    margin-bottom: 8px
  }
  .modal-postman-form-method{
    padding-top: 16px;
    margin-left: 8px;
    max-height: 500px;
    overflow: auto;
  }
  .methods-row{
    position: relative;
    // border-bottom: 1px solid #e9e9e9;
    .ant-input-sm{
      margin-top: 2px;
    }
  }

  .methods-row:nth-child(5){
     height: 67px;
  }

  .modal-postman-col{
    border-right: 1px solid #e9e9e9;
  }

  .show-more{
    color: #2395f1;
    padding-left: 8px;
    cursor:pointer;
  }

  .ant-row-flex {
    flex-wrap: nowrap
  }
  .input-component {
    position: absolute;
    top: 2px;
    right: 0;
    width: 150px;
    padding-top: 2px;
    margin-right: 16px;
  }

  .modal-postman-expression{
    .expression-item,.expression {
      color: rgba(39,56,72,0.85);
      font-size: 1.17em;
      font-weight: 500;
      line-height: 1.5em;
      padding-right: 4px;
    }

    .expression-item{
      color: #2395f1;
    }


  }

  .modal-postman-preview{
    h3{
      word-wrap: break-word;
    }
  }

  
}
