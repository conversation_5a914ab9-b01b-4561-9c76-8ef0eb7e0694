@import '../../styles/common.scss';
@import '../../styles/mixin.scss';

.footer-wrapper{
  height: 2.4rem;
  width: 100%;
  background-color: $color-bg-dark;
  overflow: hidden;
  position: relative;
  z-index: 0;
}

.footer-container{
  margin: 0 auto !important;
  padding: .48rem .24rem;
  max-width: 12.2rem;
  .icon {
    font-size: .16rem;
    margin-right: .08rem;
  }
  .title {
    color: #8898aa;
    font-size: .14rem;
    margin-bottom: .08rem;
  }
  .link {
    font-size: .14rem;
    font-weight: 200;
    color: #8898aa;
    line-height: .3rem;
    transition: color .2s;
    &:hover {
      color: $color-bg-gray;
    }
  }
}
.footItem{
  padding: 24px 2%;
  width: 25%;
  float: left;
  div{
    margin: 6px 0;
  }
  a{
    font-weight: 200;
    color: #b3bdc1;
    &:hover{
      color: white;
    }
  }
}
.copyRight{
    padding: 24px 2%;
    width: 25%;
    float: left;
    font-size: 13px;
    text-indent: 1em;
    h4{
        font-size: 14px;
        margin: 0 auto 13px;
        font-weight: 500;
        position: relative;
        text-indent: 0;
    }
}
