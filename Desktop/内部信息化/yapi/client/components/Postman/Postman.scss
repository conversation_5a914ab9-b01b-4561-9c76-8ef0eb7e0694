@import '../../styles/mixin.scss';
.postman {


  
  .adv-button {
    margin-bottom: 8px;
  }
  .pretty-editor {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    min-height: 200px;
  }
  .pretty-editor-body {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    min-height: 300px;
    min-width: 100%;
  }
  .pretty-editor-header {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    min-height: 300px;
  }

  .interface-test {
    padding: .24rem;
    .ant-checkbox-wrapper{
      display: flex;
    }
  }

  

  .insert-code{
    
    margin-right: 20px;
    .code-item{
      border: 1px solid #dbd9d9;
      padding:3px;
      line-height: 30px;
      margin-bottom: 5px;
    }
  }

  .case-script{
    min-height: 500px;
    margin: 10px;
  }

  .response-tab{    
    margin-top: 20px;
    margin-bottom: 20px;
    .ant-tabs-nav{
      background: #f7f7f7;
      border-radius: 0 0 4px 4px;      
      border: 1px solid #d9d9d9;
      width: 100%;
    }

    .header, .body{
      margin-bottom: 10px;
    }

    .header {
      padding-left: 10px;
      padding-right: 5px;
    }

    .body {
      padding-left: 5px;
      padding-right: 10px;
    }

    .response-test{
      min-height: 400px;
    }
  }

  .ant-spin-blur {
    .res-code.success {
      background-color: transparent;
    }
    .res-code.fail {
      background-color: transparent;
    }
  }
  .res-code {
    padding: .08rem .28rem;
    color: #fff;
    margin-left: -.1rem;
    margin-right: -.28rem;
    transition: all .2s;
    position: relative;
    border-radius: 2px;
  }
  .res-code.success {
    background-color: $color-antd-green;
  }
  .res-code.fail {
    background-color: $color-antd-red;
  }

  // 容器：左侧是header 右侧是body
  .container-header-body {
    display: flex;
    padding-bottom: .36rem;
    .header, .body {
      flex: 1 0 300px;
      .pretty-editor-header, .pretty-editor-body {
        height: 100%;
      }
      .postman .pretty-editor-body {
        min-height: 200px;
      }
      .ace_print-margin {
        display: none;
      }
    }
    .header {
      max-width: 400px;
    }
    .container-title {
      display: flex;
      justify-content: space-between;
      padding: .08rem 0;
    }
    .resizer {
      flex: 0 0 21px;
      position: relative;
      &:after {
        content: '';
        display: block;
        width: 1px;
        height: 100%;
        background-color: #acaaaa;
        opacity: .8;
        position: absolute;
        left: 50%;
      }
    }
    // res body 无返回json时显示text信息
    .res-body-text {
      height: 100%;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px;
    }
  }

  .has-plugin, .req-part, .resp-part {
    margin-bottom: 16px;
  }
  .url {
    display: flex;
    margin: 24px 10px;
  }
  .key-value-wrap {
    display: flex;
    align-items: center;
    margin: 0 0 5px 0;
    .key {
      flex-basis: 220px;
    }
    .value {
      flex-grow: 1;
    }
    .eq-symbol {
      margin: 0 5px;
    }
    .params-enable{
      width: 24px;
    }
  }
  .icon-btn {
    cursor: pointer;
    margin-left: 6px;
  }
  .icon-btn:hover {
    color: #2395f1;
  }

}

.env-modal{
  .ant-modal-body{
    padding: 0;
  }
}