@import '../../styles/mixin.scss';

.nav-tooltip {
  color: red;
}

.user-menu.ant-menu.ant-menu-dark .ant-menu-item-selected {
  background: #32363a;
  color: rgba(255, 255, 255, 0.67);
}
.user-menu.ant-menu-dark .ant-menu-item-selected > a {
  color: rgba(255, 255, 255, 0.67);
}

/* .header-box.css */
.header-box {
  height: .56rem;
  line-height: .56rem;
  padding: 0;
  .logo {
    position: relative;
    float: left;
    line-height: .56rem;
    height: .56rem;
    width: 56px;
    border-right: 1px solid #55616d;
    border-left: 1px solid #55616d;
    background-color: inherit;
    transition: all .2s;
    &:hover{
      background-color: #2395f1;
    }
    .href {
      text-decoration: none;
      display: block;
    }
    .logo-name {
      color: $color-white;
      font-size: .24rem;
      font-weight: 300;
      margin-left: .38rem;
    }
    .img {
      position: absolute;
      left: 0;
      top: 50%;
      left: 50%;
      transform: translate(-16px,-17px);
    }
    .ui-badge {
      position: absolute;
      right: -18px;
      top: 6px;
      width: 30px;
      height: 21px;
      background-size: 236px 21px;
      background-repeat: no-repeat;
      background-image: none;
    }
    // &:before, &:after {
    //   content: '';
    //   display: block;
    //   width: 2px;
    //   height: .56rem;
    //   background-color: #222;
    //   border-left: 1px solid #575D67;
    //   position: relative;
    //   top: 0;
    // }
    // &:before {
    //   float: left;
    //   left: -.08rem;
    // }
    // &:after {
    //   float: right;
    //   right: -.27rem;
    // }
  }

  .nav-toolbar {
    font-size: .15rem;
    float: left;

  }
  .user-menu{
    margin-top: 20px;
  }
  .user-toolbar{
    float: right;
    height: .54rem;
    display: flex;
    align-items: center;
    .item-search {
      width: 2rem;
    }
    .toolbar-li{
      float: left;
      font-size: .14rem;
      cursor: pointer;
      color: #ccc;
      margin-left: .16rem;
      transition: color .2s;
      & a {
        color: #ccc;
      }
      .dropdown-link {
        color: #ccc;
        transition: color .2s;
        // .ant-avatar-image{
        //   margin-bottom: -10px;
        // }
        // .ant-avatar > img{
        //   height: auto;
        // }
        .avatar-image{
          margin-bottom: -10px;
          display: inline-block;
          text-align: center;
          background: #ccc;
          color: #fff;
          white-space: nowrap;
          position: relative;
          overflow: hidden;
          width: 32px;
          height: 32px;
          line-height: 0;
          border-radius: 16px;
        }

        .avatar-image > img{
          height: auto;
          width:100%;
          display: bloack;
        }

      }
      .anticon.active {
        color: #2395f1;
      }
      &:hover{
        .dropdown-link {
          color: #2395f1;
        }
      }
      .name {
        margin-left: .08rem;
      }
    }
  }
}
