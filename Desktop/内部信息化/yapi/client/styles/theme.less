@import '~antd/dist/antd.less';

@ant-prefix             : ant;

// -------- Colors -----------
@primary-color          : #2395f1;
@info-color             : #2395f1;
@success-color          : #57cf27;
@error-color            : #ff561b;
@highlight-color        : #ff561b;
@warning-color          : #fac200;
@normal-color           : #d9d9d9;

// Color used by default to control hover and active backgrounds and for
// alert info backgrounds.
@primary-1: color(~`colorPalette("@{primary-color}", 1)`);  // replace tint(@primary-color, 90%)
@primary-2: color(~`colorPalette("@{primary-color}", 2)`);  // replace tint(@primary-color, 80%)
// unused
@primary-3: color(~`colorPalette("@{primary-color}", 3)`);
@primary-4: color(~`colorPalette("@{primary-color}", 4)`);
// Color used to control the text color in many active and hover states.
@primary-5: color(~`colorPalette("@{primary-color}", 5)`);  // replace tint(@primary-color, 20%)
@primary-6: @primary-color;                                 // don't use, use @primary-color
// Color used to control the text color of active buttons.
@primary-7: color(~`colorPalette("@{primary-color}", 7)`);  // replace shade(@primary-color, 5%)
// unused
@primary-8: color(~`colorPalette("@{primary-color}", 8)`);
// unused
@primary-9: color(~`colorPalette("@{primary-color}", 9)`);
// unused
@primary-10: color(~`colorPalette("@{primary-color}", 10)`);

// Base Scaffolding Variables
// ---

// Background color for `<body>`
@body-background        : #eceef1;
// Base background color for most components
@component-background   : #fff;
@heading-color          : fade(#273848, 85%);
@text-color             : fade(#0d1b3e, 65%);
@text-color-secondary   : fade(#0d1b3e, 43%);
@heading-color-dark     : fade(#fff, 97%);
@text-color-dark        : fade(#fff, 91%);
@text-color-secondary-dark: fade(#fff, 67%);
@font-size-base         : 13px;
@font-size-lg           : @font-size-base + 3px;
@line-height-base       : 1.5;
@border-radius-base     : 4px;
@border-radius-sm       : 2px;

// The background colors for active and hover states for things like
// list items or table cells.
@item-active-bg         : @primary-1;
@item-hover-bg          : @primary-1;

// ICONFONT
@iconfont-css-prefix    : anticon;
// @icon-url               : "https://at.alicdn.com/t/font_zck90zmlh7hf47vi";
@icon-url               : "/iconfont/iconfont";

// LINK
@link-color             : @primary-color;
@link-hover-color       : @primary-5;
@link-active-color      : @primary-7;
@link-hover-decoration  : none;

// Animation
@ease-out            : cubic-bezier(0.215, 0.61, 0.355, 1);
@ease-in             : cubic-bezier(0.55, 0.055, 0.675, 0.19);
@ease-in-out         : cubic-bezier(0.645, 0.045, 0.355, 1);
@ease-out-back       : cubic-bezier(0.12, 0.4, 0.29, 1.46);
@ease-in-back        : cubic-bezier(0.71, -0.46, 0.88, 0.6);
@ease-in-out-back    : cubic-bezier(0.71, -0.46, 0.29, 1.46);
@ease-out-circ       : cubic-bezier(0.08, 0.82, 0.17, 1);
@ease-in-circ        : cubic-bezier(0.6, 0.04, 0.98, 0.34);
@ease-in-out-circ    : cubic-bezier(0.78, 0.14, 0.15, 0.86);
@ease-out-quint      : cubic-bezier(0.23, 1, 0.32, 1);
@ease-in-quint       : cubic-bezier(0.755, 0.05, 0.855, 0.06);
@ease-in-out-quint   : cubic-bezier(0.86, 0, 0.07, 1);

// Border color
@border-color-base      : #d9d9d9;        // base border outline a component
@border-color-split     : #e9e9e9;        // split border inside a component
@border-width-base      : 1px;            // width of the border for a component
@border-style-base      : solid;          // style of a components border

// Outline
@outline-blur-size      : 0;
@outline-width          : 2px;
@outline-color          : @primary-color;

// Default background color for disabled states, Collapse wrappers,
// and several active and hover states.
@background-color-base  : #f7f7f7;
@background-color-active: #eee;

// Disabled states
@disabled-color         : fade(#0d1b3e, 45%);
@disabled-bg            : @background-color-base;
@disabled-color-dark    : fade(#fff, 55%);

// Shadow
@shadow-color           : rgba(0, 0, 0, .2);
@box-shadow-base        : @shadow-1-down;
@shadow-1-up            : 0 -1px 6px @shadow-color;
@shadow-1-down          : 0 1px 6px @shadow-color;
@shadow-1-left          : -1px 0 6px @shadow-color;
@shadow-1-right         : 1px 0 6px @shadow-color;
@shadow-2               : 0 2px 8px @shadow-color;

// Buttons
@btn-font-weight        : 400;
@btn-border-radius-base : @border-radius-base;
@btn-border-radius-sm   : @border-radius-base;

@btn-primary-color      : #fff;
@btn-primary-bg         : @primary-color;

@btn-default-color      : @text-color;
@btn-default-bg         : #fff;
@btn-default-border     : @border-color-base;

@btn-danger-color       : @error-color;
@btn-danger-bg          : @background-color-base;
@btn-danger-border      : @border-color-base;

@btn-disable-color      : @disabled-color;
@btn-disable-bg         : @disabled-bg;
@btn-disable-border     : @border-color-base;

@btn-padding-base       : 2px 16px;
@btn-font-size-lg       : @font-size-lg;
@btn-padding-lg         : @btn-padding-base;
@btn-padding-sm         : 0 7px;

@btn-height-base        : 28px + 4px;
@btn-height-lg          : 32px + 4px;
@btn-height-sm          : 22px + 4px;

@btn-circle-size        : @btn-height-base;
@btn-circle-size-lg     : @btn-height-lg;
@btn-circle-size-sm     : @btn-height-sm;

@btn-group-border       : @primary-7;

// Radio buttons
@radio-button-bg           : @btn-default-bg;
@radio-button-color        : @btn-default-color;


// Layout
@layout-body-background      : #eceef1;
@layout-header-background    : #32363a;
@layout-header-height        : 56px;
@layout-header-padding       : 0 0;
// @layout-footer-padding       : 24px 50px;
// @layout-sider-background     : @layout-header-background;
// 侧栏背景颜色
@layout-sider-background     : #fff;
// @layout-trigger-height       : 48px;
// @layout-trigger-background   : tint(@heading-color, 20%);
// @layout-trigger-color        : #fff;
// @layout-zero-trigger-width   : 36px;
// @layout-zero-trigger-height  : 42px;

// Animation
@animation-duration-slow: .3s -.1s; // Modal
@animation-duration-base: .2s;
@animation-duration-fast: .1s; // Tooltip

// Form
// ---
@label-required-color        : @highlight-color;
@label-color                 : @heading-color;
@form-item-margin-bottom     : 24px;
// 处理添加环境配置下边距重合的 bug
.ant-form-item > .ant-form-item, .ant-form-item :not(.ant-form) > .ant-form-item {
  margin-bottom: 16px;
}
@form-item-trailing-colon    : true;

// Input
// ---
@input-height-base           : 28px + 4px;
@input-height-lg             : 32px + 4px;
@input-height-sm             : 22px + 4px;
@input-padding-horizontal    : 7px + 2px;
@input-padding-vertical-base : 4px + 2px;
@input-padding-vertical-sm   : 1px + 2px;
@input-padding-vertical-lg   : 6px + 2px;
@input-placeholder-color     : @disabled-color;
@input-color                 : @text-color;
@input-border-color          : @border-color-base;
@input-bg                    : #fff;
@input-addon-bg              : #eee;
@input-hover-border-color    : @primary-color;
@input-disabled-bg           : @disabled-bg;

// Tooltip
// ---
//* Tooltip max width
@tooltip-max-width: 250px;
//** Tooltip text color
@tooltip-color: #fff;
//** Tooltip background color
@tooltip-bg: rgba(64, 64, 64, .85);
//** Tooltip arrow width
@tooltip-arrow-width: 5px;
//** Tooltip distance with trigger
@tooltip-distance: @tooltip-arrow-width - 1px + 4px;
//** Tooltip arrow color
@tooltip-arrow-color: @tooltip-bg;

// Popover
// ---
//** Popover body background color
@popover-bg: #fff;
//** Popover text color
@popover-color: @text-color;
//** Popover maximum width
@popover-min-width: 177px;
//** Popover arrow width
@popover-arrow-width: 4px;
//** Popover arrow color
@popover-arrow-color: @popover-bg;
//** Popover outer arrow width
@popover-arrow-outer-width: (@popover-arrow-width + 1px);
//** Popover outer arrow color
@popover-arrow-outer-color: fadeout(@border-color-base, 30%);
//** Popover distance with trigger
@popover-distance: @popover-arrow-width + 4px;

// Modal
// --
@modal-mask-bg: rgba(55, 55, 55, 0.6);

// Progress
// --
@process-default-color: @primary-color;
@progress-remaining-color: @background-color-base;

// Menu
// ---
@menu-dark-bg: @layout-header-background;
@menu-dark-submenu-bg: #333;
@menu-collapsed-width: 64px;
// .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
//   background-color: rgba(35,149,241, .15);
// }
// .ant-menu-item:active, .ant-menu-submenu-title:active {
//   background-color: rgba(35,149,241, .15);
// }

// Spin
// ---
@spin-dot-size-sm: 14px + 2px;
@spin-dot-size: 20px + 3px;
@spin-dot-size-lg: 32px + 4px;

// Table
// --
@table-header-bg: #eee;
@table-header-sort-bg: @background-color-active;
@table-row-hover-bg: @primary-1;
@table-selected-row-bg: #fafafa;
@table-padding-vertical: 16px;
@table-padding-horizontal: 8px + 2px;

// Tag
// --
@tag-default-bg: #f3f3f3;
@tag-default-color: @text-color;
@tag-font-size: @font-size-base;

// TimePicker
// ---
@time-picker-panel-column-width: 56px;
@time-picker-panel-width: @time-picker-panel-column-width * 3;
@time-picker-selected-bg: @background-color-base;

// Carousel
// ---
@carousel-dot-width: 16px + 4px;
@carousel-dot-height: 3px + 2px;
@carousel-dot-active-width: 24px + 4px;

// Badge
// ---
@badge-height: 20px + 4px;
@badge-dot-size: 8px + 4px;
@badge-font-size: @font-size-base + 2px;

// Rate
// ---
@rate-star-color: #f5a623;
@rate-star-bg: #e9e9e9;

// Card
// ---
@card-head-height: 48px;
@card-head-color: @heading-color;
@card-head-background: @component-background;

// Tabs
// ---
// @tabs-card-head-background: #f9f9f9;
// @tabs-title-font-size: @font-size-lg;
.ant-tabs-bar {
    margin-bottom: 0;
}

// BackTop
@back-top-color: #fff;
@back-top-bg: rgba(64, 64, 64, 0.4);
@back-top-hover-bg: rgba(64, 64, 64, 0.6);

// Avatar
@avatar-size-base: 32px;
@avatar-size-lg: 40px;
@avatar-size-sm: 24px;
@avatar-font-size-base: 18px;
@avatar-font-size-lg: 24px;
@avatar-font-size-sm: 14px;
@avatar-bg: #ccc;
@avatar-color: #fff;
@avatar-border-radius: @border-radius-base;
