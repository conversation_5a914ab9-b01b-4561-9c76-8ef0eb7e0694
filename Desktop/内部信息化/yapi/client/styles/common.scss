@charset "utf-8";


html {
  font-size:100px;
}
html, body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>,
             "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
             Sim<PERSON>un, sans-serif;
  margin: 0;
  padding: 0;
}

html {
  // background-color: #32363a;
}

::selection {
  background-color: #2395f1;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
}
/* 外层轨道 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(255,0,0,0.3);
  // background-color: 
  background: rgba(255, 255, 255, 0.1);
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: rgba(0,0,0,0.2);
  -webkit-box-shadow: inset006pxrgba(0,0,0,0.5);
}
::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0,0,0,0.2);
}

div, article, p, table, tr, td, th, ul, ol, li, h1, h2, h3, form, dl, dt, dd {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

ul {
  list-style-type: none;
}

a {
  text-decoration: none;
  &:active, &:hover, &:visited, &:focus{
    text-decoration: none
  }
}

a:hover {
  color: #636363;
}

em {
  font-style: normal;
}

[data-reactroot], .g-main, .router-main {
  height: 100%;
}
// 页面最外层元素 样式
.router-main {
  padding-bottom: .24rem;
  min-height: 100%;
  height: auto !important;
  height: 100%;
  margin-bottom: -2.4rem;
  background-color: #eceef1;
  &::after {
    content: '';
    display: block;
    height: 2.4rem;
  }
}
.m-tab {
  .ant-tabs-nav-wrap {
    background-color: #eceef1;
  }
}

.hidden {
  display: none;
}

// 布局容器 guitter: 16px
.g-row {
  min-width: 7.52rem;  // Col宽度 (752+16)/24 = 32
  margin: 0 auto;
  padding: 0 .24rem;
}

.m-container {
  margin: .24rem auto;
  padding: .24rem;
}

.ant-dropdown .user-menu {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
}

// confirm 框内边距过大
.ant-confirm .ant-modal-body{
  padding: .24rem !important;
}

.card-panel {
  padding: .36rem .24rem 0;
}

.pannel-without-tab {
  min-height: 5rem;
}

.panel-title {
  margin-bottom: .16rem;
  border-left: 3px solid #2395f1;
  padding-left: 8px;
  .title {
    font-weight: normal;
  }
  .desc {
    font-size: 13px;
    color: #919191;

  }
}

@media (max-width: 768px) {
  html {
    width: min-content !important;
  }
}

.tag-status {
  &::before {
    content: '';
    display: inline-block;
    margin-right: 6px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    position: relative;
    bottom: 1px;
  }
  &.done::before {
    background-color: #57cf27;
  }
  &.undone::before {
    background-color: #ff561b;
  }
}


.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab{
  border: none;
}
.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-active{
  border: none;
}

.ant-tabs.ant-tabs-card > .ant-tabs-content{
  margin-top: -1px;
}

.tabs-large .ant-tabs-nav-container{
  font-size: 16px;
}

.ant-tree li .ant-tree-node-content-wrapper{
  padding: 3px 5px;
  height: unset;
  // line-height: 25px;
}

.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected{
  background-color: #d5ebfc;
}



// 页面级底部固定 需要添加padding以保证页面底部不被覆盖
// .has-affix-footer {
//   padding-bottom: .92rem;
// }

.popover-index {
  max-width: 3.2rem;
  .ant-popover-title {
    height: auto;
  }
  .title-container {
    padding: .16rem 0;
    .title {
      text-align: center;
    }
  }
  .btn-container {
    text-align: center;
    .btn {
      margin: 0 .04rem;
    }
  }
}

.study-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,.35);
  z-index: 2;
}

// tooltip 中的链接
.link-tooltip {
  color: #56b2fd;
  &:hover {
    color: #2395f1;
  }
}