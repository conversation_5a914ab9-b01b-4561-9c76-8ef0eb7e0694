@import '../../styles/mixin.scss';

$color-white : #fff;
$color-blue-lighter : #f1f5ff;
$color-blue-grey-lighter : #f7fafc;
$color-grey-lighter : #F7F7F7;
$color-blue-light: #5dade2;
$color-black-lighter: #404040;
$color-text-dark: #2e2e5a;
$color-text-light: #6d7c90;
$color-bg-lightblue: #c6e2ff;
.g-body {
  position: relative;
}
.home-header {
  font-size: 0;
  .item {
    text-decoration: none;
    display: inline-block;
    color: #fff;
    font-size: .17rem;
    padding: .16rem .24rem;
    -webkit-font-smoothing: antialiased;
    &:hover {
      color: #27cdfd;
    }
  }
}

// 按钮组
.btn-group {
  padding: .3rem .24rem;
  .btn-home {
    font-size: .15rem;
    font-weight: 200;
    letter-spacing: 1px;
    border: none;
    line-height: .4rem;
    height: .4rem;
    padding: 0 .24rem;
    margin-right: .24rem;
    box-shadow: 0 4px 6px rgba(50,50,93,.11), 0 1px 3px rgba(0,0,0,.08);
    transform: translateY(0);
    transition: all .2s;
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 7px 14px rgba(50,50,93,.1), 0 3px 6px rgba(0,0,0,.08);
    }
    &:active {
      transform: translateY(1px);
    }
  }
  .btn-login {
    background-color: #32325d;
    &:hover {
      background-color: #43459a;
    }
    &:active, &:focus {
      color: #e6ebf1;
      background-color: #32325d;
    }
  }
  .btn-home-normal {
    border-radius: 4px;
    color: #43459a;
    &:hover {
      color: #7795f8;
    }
    &:hover, &:focus {
      background-color: #f6f9fc;
    }
    background-color: #fff;
    cursor: pointer;
  }
}

.m-bg {
  position: absolute;
  left: 0;
  top: -400px;
  height: 1000px;
  width: 100%;
  transform: skewY(-11deg);
  background-image: linear-gradient(-20deg, #21d4fd 0%, #b721ff 100%);
  .m-bg-mask {
    position: absolute;
    height: 180px;
  }
  .m-bg-mask0 {
    bottom: 0;
    left: 0;
    width: 30%;
    background-image: linear-gradient(120deg, #6ab3fd 0%, #8ba3fd 102%);
  }
  .m-bg-mask1 {
    bottom: 180px;
    right: 0;
    width: 36%;
    background-image: linear-gradient(120deg, #28c5f5 0%, #6682fe 100%);
  }
  .m-bg-mask2 {
    bottom: 540px;
    left: 0;
    width: 20%;
    height: 240px;
    background-image: linear-gradient(120deg, #8121ff 0%, #5e5ef7 100%);
  }
  .m-bg-mask3 {
    bottom: 540px;
    left: 20%;
    width: 70%;
    height: 240px;
    background-image: linear-gradient(-225deg, #5f2bff 0%, #6088fe 48%, #22ccf6 100%);
  }
}
.home-main {
  background-color: #fff;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  .main-one{
    height: 600px;
    .home-des{
      padding: 1rem 0 0;
      color: #fff;
      .title{
        font-size: .6rem;
      }
      .detail{
        font-size: .2rem;
      }
      .logo {
        display: flex;
        align-items: center;
        padding: 0 .24rem;
      }
      .svg {
        animation: spin 5s linear infinite;
      }
      .name {
        vertical-align: middle;
        font-size: .48rem;
        margin-left: .24rem;
        font-weight: 200;
      }
      .detail {
        padding: .24rem;
        font-size: .24rem;
        font-weight: 200;
      }
      .desc {
        font-size: .18rem;
      }
    }
    .login-form{
      color: $color-white;
    }
    .main-one-left{
      padding-right: .15rem;
      margin-top: .2rem;
    }
    .main-one-right{
      padding-left: .5rem;
      padding-top: .3rem;
    }
  }
  .user-home{
    display: flex;
    align-items: center;
    height: 100%;
    margin: 1rem auto 0;
    .user-des{
      margin: 0 auto .5rem;
      text-align: center;
      .title{
        font-size: .8rem;
        margin-bottom: .2rem;
      }
      .des{
        font-size: .25rem;
        margin-bottom: .3rem;
      }
      .btn{
        button{
          font-size: .2rem;
          line-height: .2rem;
          height: .5rem;
          padding: .15rem .5rem;
        }
      }
    }
  }
  .main-part{
    padding: 1.5rem 0;
    height: 5.8rem;
    &:nth-child(odd){
      background-color: $color-blue-lighter;
    }
    &:nth-child(even){
      background-color: $color-white;
    }
  }
  .feat-part{
    padding: 1.5rem 0;
    background-color: $color-white;
  }
  .section-feature {
    min-height: 6rem;
  }
  .container{
    margin: 0 auto;
    height:100%;
    position: relative;
    max-width: 12.2rem;
  }
  .feat-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    .feat-img {
      height: 1.2rem;
      width: 1.2rem;
      border-radius: 100%;
      margin-bottom: .2rem;
      color: $color-white;
      i {
        line-height: 1.2rem;
        font-size: .6rem;
      }
    }
    .feat-title {
      font-size: .16rem;
      line-height: .3rem;
    }
    &:first-child {
      .feat-img {
        background-color: rgb(248, 88, 96);
      }
    }
    &:nth-child(2) {
      .feat-img {
        background-color: #f9bb13;
      }
    }
    &:nth-child(3) {
      .feat-img {
        background-color: #20ab8e;
      }
    }
    &:nth-child(4) {
      .feat-img {
        background-color: rgb(66, 165, 245);
      }
    }
  }
  .img-container{
    width: 100%;
    position: absolute;
    top: .74rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: right;
    .img{
      width: 7.12rem;
      border-radius: 4px;
      box-shadow : 0 30px 60px rgba(0,0,0,0.2);
    }
  }
  .m-skew {
    position: relative;
    .skew-container {
      padding: 0 1rem;
    }
    .m-skew-bg {
      position: absolute;
      left: 0;
      top: 5%;
      height: 600px;
      width: 100%;
      transform: skewY(-11deg);
      background-image: linear-gradient(180deg, #93a5cf 0%, #e4efe9 100%);
      .m-bg-mask {
        position: absolute;
        height: 200px;
      }
      .m-bg-mask0 {
        bottom: 0;
        left: 0;
        width: 30%;
        background-image: linear-gradient(120deg, #6ab3fd 0%, #c1cfde 102%);
      }
      .m-bg-mask1 {
        bottom: 200px;
        right: 0;
        width: 36%;
        background-image: linear-gradient(219deg, #84a1ce 0%, #e4efe9 100%);
      }
      .m-bg-mask2 {
        top: 0;
        left: 0;
        width: 30%;
        background-image: linear-gradient(219deg, #93a5cf 0%, #d7e3e5 100%);
      }
    }
  }
}

.home-section {
  text-align: center;
  -webkit-font-smoothing: antialiased;
  .title {
    color: $color-text-dark;
    line-height: .32rem;
    margin-bottom: .08rem;
    font-size: .24rem;
  }
  .desc {
    color: $color-text-light;
    font-size: .16rem;
  }
  .section-item {
    text-align: left;
    padding: .24rem;
    .img {
      width: .48rem;
      height: .48rem;
      background-image: linear-gradient(-20deg, #21d4fd 0%, #b721ff 100%);
      border-radius: 50%;
      text-align: center;
      line-height: .48rem;
      font-size: .24rem;
      color: #fff;
      margin-bottom: .24rem;
    }
    .title {
      color: $color-text-dark;
      font-size: .2rem;
    }
    .desc {
      color: $color-text-light;
      font-size: .16rem;
    }
  }
  .row-card {
    margin-top: .48rem;
    padding: 0 .24rem;
  }
  .section-card {
    padding-bottom: 1rem;
    .ant-card {
      font-size: .17rem;
      border-radius: .04rem;
      box-shadow: 0 16px 35px rgba(50,50,93,.1), 0 5px 16px rgba(0,0,0,.07);
      border: none;
    }
    .ant-card:not(.ant-card-no-hovering):hover {
      box-shadow: 0 16px 35px rgba(50,50,93,.1), 0 5px 16px rgba(0,0,0,.07);
    }
    .ant-card-head {
      background-color: $color-blue-grey-lighter;
      border-top-left-radius: .04rem;
      border-top-right-radius: .04rem;
    }
    .ant-card-head-title {
      font-size: .17rem;
      color: $color-text-dark;
    }
    .ant-card-body {
      text-align: left;
      padding: 0;
    }
    .mock-desc {
      padding: .32rem;
      min-height: 8em;
    }
    padding: .08rem;
  }
  .code {
    color: $color-text-light;
    background-color: $color-blue-grey-lighter;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    .orderNum {
      background-color: $color-bg-lightblue;
      display: inline-block;
      text-align: center;
      width: .4rem;
      margin-right: .5em;
      user-select: none;
    }
    .orderNum-first {
      padding-top: .5em;
    }
    .orderNum-last {
      border-bottom-left-radius: 4px;
      padding-bottom: .5em;
    }
    .string {
      color: #ff561b;
    }
    .number {
      color: #57cf27;
    }
    .keyword {
      color: #2359f1;
    }
    .item {
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
    }
  }
  .mock-after {
    .ant-card-head {
      background-color: $color-bg-lightblue;
    }
    .ant-card-head-title {
      color: #4074af;
    }
  }
}

.section-manage {
  .section-card{
    padding-top: .24rem;
    .ant-card {
      border-radius: .04rem;
    }
    .ant-card-body {
      padding: 0;
    }
  }
  .section-block {
    padding: .24rem;
    .item {
      font-size: .14rem;
    }
  }
  .block-first {
    background-color: #5f48fe;
    border-top-left-radius: .04rem;
    border-top-right-radius: .04rem;
  }
  .block-second {
    background-color: #5f79fe;
  }
  .block-third {
    background-color: #3ab1f9;
    border-bottom-left-radius: .04rem;
    border-bottom-right-radius: .04rem;
  }
  .ant-card-body, h4 {
    color: #fff;
  }
  .manage-word {
    text-align: left;
    padding-left: .48rem;
    .icon {
      width: .72rem;
      height: .72rem;
      line-height: .72rem;
      text-align: center;
      background-color: #5f48fe;
      border-radius: 50%;
      font-size: .4rem;
      color: #fff;
      margin-bottom: .24rem;
    }
    .desc {
      margin-bottom: .16rem;
    }
  }
}

.row-tip {
  margin-top: .48rem;
  padding-top: .48rem;
  padding-bottom: .24rem;
  background-color: #ececec;
  .container {
    display: flex;
    align-items: center;
    max-width: 12.2rem;
    .tip-title {
      flex: 2;
    }
    .tip-btns {
      flex: 1;
    }
  }
  .btn-group {
    white-space: nowrap;
  }
  .title {
    -webkit-font-smoothing: antialiased;
    padding-left: .24rem;
    color: #2e2e5a;
    line-height: .32rem;
    margin-bottom: .08rem;
    font-size: .24rem;
  }
  .desc {
    -webkit-font-smoothing: antialiased;
    padding-left: .24rem;
    color: #6d7c90;
    font-size: .16rem;
  }
}

// LOGO 旋转动画
@keyframes spin
{
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
@media (max-width: 1200px) {
  .home-header, .home-des {
    text-align: center;
  }
  .home-main .main-one .home-des .logo {
    justify-content: center;
  }
}
