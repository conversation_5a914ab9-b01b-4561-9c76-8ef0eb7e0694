@import '../../styles/mixin.scss';

.news-box {
  @include row-width-limit;

  display: -webkit-box;
  -webkit-box-flex: 1;
  margin: 0px auto 0 auto;
  font-size: 0.14rem;
  background: #FFF;
  display: block;

  .news-timeline{
    padding: 24px;
    padding-left: 125px;
    color: #6b6c6d;
    .ant-timeline-item{
      min-height: 60px;
    }
    .ant-timeline-item-head{
      width: 30px;
      height: 30px;
      left: -8px;
      top: -4px;
      border-color:#e1e3e4;
    }
    .logusername{
      color: #4eaef3;
      padding: 0px 16px 0px 8px;
      cursor: pointer;
    }
    .logtype{
      padding-right: 16px;
    }
    .logtime{
      padding-right: 16px;

    }
    .logcontent{
      display: block;
      padding-left: 8px;
      line-height: 24px;
    }
    .logoTimeago{
      position: absolute;
      left: -80px;
      top: 5px;
      color: #c0c1c1;
    }
    .logbidden{
      color: #c0c1c1;
      cursor: default;
      line-height: 30px;
      padding-left: 30px;
    }
    .loggetMore{
      line-height: 30px;
      padding-left: 30px;
      color: #4eaef3;
    }
  }
  .logHead{
    height: 80px;
    width: 100%;
    border-bottom: 1px solid #e9e9e9;
    padding: 24px 0px;
    overflow: hidden;
    .breadcrumb-container{
      float: left;
      min-width:100px;
    }
    .Mockurl{
      width: 500px;
      float: right;
      color: #7b7b7b;
      >span{
        float: left;
        line-height: 30px;
      }
      p{
        width: 60%;
        display: inline-block;
        position: relative;
        padding: 4px 7px;
        height: 28px;
        cursor: text;
        font-size: 13px;
        color: rgba(0,0,0,.65);
        background-color: #fff;
        background-image: none;
        border: 1px solid #d9d9d9;
        -webkit-transition: all .3s;
        transition: all .3s;
        overflow-x:auto;
      }
      button{
        float: right;
      }
    }
  }
}
