@import '../../styles/mixin.scss';

.g-doc {
  margin: 0 auto 0.24rem;
}

/* .user-box.css */
.user-box {
  @include row-width-limit;
  padding: 0;
  display: -webkit-box;
  -webkit-box-flex: 1;
  margin: 0 auto;
  margin-top: 24px;
  .user-list {
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    background: #fff;
    border-radius: 4px;
    min-height: 5rem;
    .search {
      padding: 5px;
      background-color: #eee;
    }
    ul {
      border: none;
    }
    .ant-menu-item {
      font-size: 18px;
    }
  }
  .user-name {
    padding: 0.24rem;
    background-color: #34495e;
    color: white;
    font-size: 18px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    span {
      margin-right: 5px;
    }
  }
  .router-content {
    min-height: calc(100% - 2.47rem);
  }
  .user-table {
    -webkit-box-flex: 1;
    padding: 24px;
    background: #fff;
    min-height: 5rem;
    .ant-table-wrapper table {
      // font-size: .14rem;

      a {
        // font-size: 13px;
      }
    }
    .user-search-wrapper {
      position: relative;
      .ant-input-search {
        position: absolute;
        right: 0;
        top: 0;
        width: 250px;
      }
    }
  }

  .user-profile {
    -webkit-box-flex: 1;
    background: #fff;
    min-height: 5rem;
    position: relative;

    .edit-buttons {
      .edit-button {
        text-align: center;
        margin-right: 10px;
      }
    }

    .bacToPer {
      position: absolute;
      right: 8px;
      top: 8px;
      z-index: 3;
    }
    .user-item-body {
      width: 95%;
      margin: 0px auto;
      padding: 32px;
      position: relative;
      // box-shadow: 0 4px 6px rgba(50, 50, 93, 0.3), 0 1px 3px rgba(0, 0, 0, 0.01);
      h3 {
        font-size: 20px;
        font-weight: 500;
        margin-bottom: 16px;
      }
      .user-item-mask-top {
        position: absolute;
        top: 0px;
        left: 0px;
        height: 100%;
        width: 100%;
        background: black;
        z-index: 2;
        opacity: 0.7;
      }
      .user-item-mask {
        position: absolute;
        top: 0px;
        left: 0px;
        height: 0px;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background-color: rgba(0, 0, 0, 0.7);
      }
      .user-item {
        position: relative;
        z-index: 3;
        padding: 8px 0px;

        .maoboli {
          // background-color: rgba(225,225,225,0.16);
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0px;
          top: 0px;
        }
      }
    }
    .user-item {
      min-height: 35px;
      line-height: 35px;
      margin: 5px;
      margin-left: 0px;
      font-size: 14px;
      #old_password,
      #password,
      #verify_pass {
        margin-top: 20px;
      }
      #old_password {
        margin-top: 0px;
      }
      .ant-col-12 {
        .ant-input {
          width: 60%;
          margin-right: 16px;
        }
      }
      .ant-col-4 {
        color: rgba(71, 86, 99, 0.8);
        font-weight: 500;
        padding: 0px 16px;
        text-indent: 0.7em;
        margin-right: 30px;
        white-space: nowrap;
        text-align: right;
        width: 130px;
      }
      .text {
        padding-right: 16px;
      }
      .text-button {
        color: #657289;
        cursor: pointer;
      }
    }
  }

  .user-nav {
    border-bottom-left-radius: 0.04rem;
    border-bottom-right-radius: 0.04rem;
  }
  .avatar-uploader {
    border: none;
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.31), 0 1px 3px rgba(0, 0, 0, 0.08);
  }
  .avatar-uploader,
  .avatar-uploader-trigger {
    width: 100px;
    height: 100px;
    overflow: hidden;
    border-radius: 50px;
  }
  .avatar {
    width: 100px;
    min-height: 100px;
  }
  .avatarImg {
    width: 100px;
    border-radius: 50px;
    overflow: hidden;
    height: 100px;
    background-color: white;
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.31), 0 1px 3px rgba(0, 0, 0, 0.08);
    margin-left: 60px;
    img {
      width: 100%;
    }
  }
  .avatar-uploader {
    display: block;
    cursor: pointer;
    height: 100px;
  }
  .ant-upload-list {
    display: none;
  }
  .avatar-box {
    width: 100px;
    position: relative;
    margin-left: 60px;
  }
  .avatarCon {
    // background: gray;
    padding: 16px 0px;
    width: 100%;
    overflow: hidden;
    position: relative;
  }

  .avatar-uploader-trigger {
    display: table-cell;
    vertical-align: middle;
    font-size: 28px;
    color: #999;
  }
  .avatarChange {
    display: block;
    width: 300px;
    text-align: center;
    padding: 8px;
    margin-left: -100px;
    color: #ececec;
    position: absolute;
    top: 0px;
  }
}
