.postman-dataImport{
    display: flex;
    .dataImportCon{
        min-width: 304px;
        background-color: #ececec;
        padding: 16px;
        border-radius: 4px;

        .ant-upload-drag{
            padding: 16px;
            background-color: white;
        }
        .dataImportTile{
            color: #2395f1;
            padding: 16px 0px;
            font-weight: 500;
            width: 100%;
            .ant-select{
                width: 100%;
            }
        }

        .dataExport{
          padding-bottom: 16px;
          font-weight: 500;
          width: 100%;
        }

        .dataSync{
          padding-top: 16px;
          font-weight: 500;
          width: 100%;
          .label{
            padding-right: 8px;
            width: 150px;
            display: inline-block;
          }
          .label:after {
            content: ":";
            margin: 0 8px 0 2px;
            position: relative;
            top: -.5px;
          }
        }

        .import-content {
          margin-top: 16px;
          height: 180px;
        }

        .url-import-content {
          text-align: center;
          .url-btn{ 
            margin-top: 16px;
          }
        }

        .export-content{
            text-align: center;
        }
        .export-desc{
            padding-bottom: 15px;
        }
        .export-button{
            width: 100px;
            height:35px;
        }

        .wiki-btn {
          margin-left: 8px;
        }
    }
}

.postman-dataImport-modal{
  
  .postman-dataImport-modal-content{
    max-height: 600px;
    min-width: 534px;
    overflow-y: scroll;
    padding-top: 8px;

  }
  .postman-dataImport-show-diff{
    padding:  4px 0;
  
  }

  .info{
    font-weight: 400;
    font-size: 16px;
    padding-top: 24px;
  }
  
}

.dataImport-confirm{
  .ant-modal-content .ant-confirm-btns{
    margin-top: 10px;
  }
}


