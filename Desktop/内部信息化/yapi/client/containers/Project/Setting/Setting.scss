
.form-item {
  margin-bottom: .16rem;
}
.breakline {
  margin-top: .18rem;
  margin-bottom: .18rem;
  border: 0;
  border-top: 1px solid #eeeeee;
}
.card-danger {
  border-color: #ff561b;
  border-radius: 4px;
  .ant-card-body {
    display: flex;
    align-items: center;
    padding: .24rem !important;
  }
  .card-danger-content {
    flex: 1;
  }
  .card-danger-btn {
    flex-grow: 0;
    flex-shrink: 1;
  }
}

.setting-project-member {
  .btn{
    margin-left: 8px;
  }
  .m-user-name {
    padding-right: 16px;
  }

}

.setting-group {
  margin-top: .48rem;
  border-radius: 2px;
  border-bottom: 1px solid #eee;
  .ant-card-head {
    background-color: #eee;
    padding: 0 .08rem !important;
  }
  .ant-card-head-title {
    font-size: .12rem;
    float: inherit;
  }
  .ant-card-body {
    padding: 0 !important;
  }
  .card-item {
    padding: .1rem .15rem;
    position: relative;
    .item-img {
      width: .2rem;
      height: .2rem;
      margin-right: .08rem;
      vertical-align: middle;
    }
    .item-name {
      position: absolute;
      left: .43rem;
      top: 50%;
      transform: translateY(-50%);
    }
    .item-role {
      position: absolute;
      right: .15rem;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .card-item + .card-item {
    border-top: 1px solid #eee;
  }
}

.project-setting {
  .setting-logo {
    text-align: right;
    padding: .24rem;
    cursor: pointer;
  }
  .setting-intro {
    padding: .24rem;
    height: 1.48rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .ui-title {
      font-size: .32rem;
      font-weight: normal;
      width: 100%;
    }
    .ui-desc {
      font-size: .16rem;
    }
  }
  .ui-logo {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    font-size: .5rem;
    color: #fff;
    background-color: #2395f1;
    line-height: 1rem;
    box-shadow: 0 4px 6px rgba(50,50,93,.11), 0 1px 3px rgba(0,0,0,.08);
    position: relative;
    &:after {
      opacity: 0;
      content: '点击修改';
      display: block;
      transition: all .4s;
      position: absolute;
      left: 0;
      top: 0;
      border-radius: 50%;
      font-size: .14rem;
      color: #fff;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0, .25);
    }
    &:hover:after {
      opacity: 1;
    }
  }
}

.change-project-container {
  max-width: 320px;
  .ant-popover-inner {
    text-align: center;
  }
  .ant-popover-title {
    padding: 8px .16rem;
    height: auto;
  }
  .ant-radio-button-wrapper {
    font-size: 16px;
    border: none;
    &:first-child {
      border: none;
    }
    &:not(:first-child)::before {
      display: none !important;
    }
  }
  .ant-radio-button-wrapper-checked {
    box-shadow: none;
    color: #fff;
    background-color: #2395f1;
    border-radius: 4px;
  }
  .color {
    // .ant-radio-button-wrapper {
    //   &:first-child {
    //     border: none;
    //   }
    // }
    .ant-radio-button-wrapper-checked {
      border-radius: 0;
      &:hover {
        border: none;
        box-shadow: none;
      }
    }
  }
}

.danger-container {
  margin-top: .48rem;
}
.btnwrap-changeproject {
  text-align: center;
  padding: .16rem 0;
  background: #fff;
  background-color: #fff;
  margin: 0 -.4rem;
  // background-image: linear-gradient(45deg, #d9d9d9 25%, transparent 0),linear-gradient(45deg, transparent 75%, #d9d9d9 0);
  background-size: 4px 4px;
  .btn-save {
    font-size: .15rem;
    font-weight: 200;
    letter-spacing: 1px;
    border: none;
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
    transform: translateY(0);
    transition: all .2s;
    &:hover {
      transform: translateY(-1px);
    }
    &:active {
      transform: translateY(1px);
    }
  }
}




.project-env{
  // 环境配置中首个item的删除按钮定位调整
  .ant-row-flex {
    display: flex;
    flex-flow: row wrap;
    height: 60px;
  }
}

// 危险操作
.danger-container {
  .title {
    margin-bottom: .48rem;
    text-align: center;
    .content {
      color: rgba(39, 56, 72, 0.65);
      margin-bottom: .16rem;
    }
  }
}

.radio.ant-radio-wrapper{
  line-height: unset
}
