.caseContainer{
    padding: 24px;
    font-size: 13px;
    // display: flex;
    overflow: hidden;
    >div{
        margin: 8px 0px;
        // background-color: #ececec;
        padding: 16px;
        width: 100%;
        box-sizing: border-box;
        float: left;
        .col<PERSON><PERSON>{
            font-weight: bold;
            text-align: left;
            width: 100px;
            padding-left: 10px;
        }

        .colName{
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
    }
    .col-title{margin-bottom: 5px}
    .colValue.href {
      color: #2395f1;
      cursor: pointer;
    }
    .ace_print-margin{
        display: none;
    }

    .interface-url-icon{
      padding-left: 8px;
    }
    .colBody{
        .query-icon {
          display: inline-block;
          width: .13rem;
          margin-right: 4px;
          position: relative;
          &.text:after {
              content: 'T';
              display: block;
              position: absolute;
              right: 2px;
              bottom: -2px;
              transform: scale(.7);
          }
        }
    }
    .colDesc{
        margin-bottom: 0px;
        padding-bottom: 0px;
    }
    .ant-table-thead {
        th{
            color: #6d6c6c;
            font-weight: normal;
        }
        tr{
            // background-color: black;
        }
    }
    .ant-table-thead>tr>th{
        background: #f7f7f7;
    }
    .colMethod{
        .colValue{
            display: inline-block;
            border-radius: 4px;
            color: #00a854;
            background: #cfefdf;
            border-color: #cfefdf;
            height: 23px;
            line-height: 23px;
            text-align: center;
            font-size: 10px;
            margin-right: 7px;
            padding: 0 5px;
        }
    }
    .colstatus{
        .colValue{
            height: 23px;
            padding: 0 5px;
            line-height: 23px;
            display: inline-block;
            border-radius: 4px;
            color: white;
        }
    }
    .user-img {
      width: .3rem;
      height: .3rem;
      border-radius: 50%;
      border: 2px solid #eee;
      vertical-align: middle;
      margin-right: 4px;
    }
    .tag-method {
      padding: 4px 6px;
      margin-right: 8px;
      border-radius: 4px;
      vertical-align: middle;
    }
}

.colHalf {
    width: 50%;
    float: left;
    margin-bottom: .16rem;
    .colKey{
        padding-bottom: 0px;
        text-align: right;
    }
}

.panel-view {
  .row {
    margin-bottom: .16rem;
    line-height: 36px;
  }
  .user-img {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(0, -50%);
  }
  .user-name {
    padding-left: .38rem;
  }
}
