@import '../../styles/common.scss';

// .login-body {
//   background-color: #fff;
// }
.login-container {
  padding-bottom: .6rem;
}

.login-form-button {
  background-image: linear-gradient(to right, #6d69fe 0%, #48a0fa 100%) !important;
  border: none !important;
  margin-top: .2rem;
  width: 100%;
}

.ant-form-item {
  margin-bottom: .1rem;
}

.qsso-breakline{
  display: flex;
  align-items: center;
  color: #6d7c90;
  margin: .2rem auto;
  &:before, &:after{
    content: "";
    display: inline-block;
    height: .02rem;
    flex: 1;
    border-top: .01rem solid #6d7c90;
  }
  .qsso-breakword{
    padding: 0 .1rem;
  }
}

.card-login {
  margin-top: 1.6rem;
  margin-bottom: 1.6rem;
  border-radius: .04rem;
  position: relative;
  .login-logo {
    font-size: 0;
    position: absolute;
    left: 50%;
    top: 0;
    background-image: linear-gradient(-20deg, #21d4fd 0%, #b721ff 100%);
    transform: translate(-50%, -50%);
    padding: .16rem;
    border-radius: 50%;
    box-shadow: 0 4px 6px rgba(50,50,93,.11), 0 1px 3px rgba(0,0,0,.08);
  }
  .login-title {
    text-align: center;
    padding-top: .5rem;
    font-size: .4rem;
    font-weight: 200;
    color: #2e2e5a;
  }
  .svg {
    animation: spin 5s linear infinite;
  }
}
